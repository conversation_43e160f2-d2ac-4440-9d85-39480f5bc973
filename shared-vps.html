<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared VPS Hosting Plans - X-ZoneServers | Cost-Effective Virtual Servers with Shared 1Gbps</title>
    <meta name="description" content="X-ZoneServers Shared VPS hosting with 1Gbps shared bandwidth, SSD storage, unmetered traffic. Budget-friendly virtual servers starting from €4.99/month.">
    <meta name="keywords" content="shared VPS, budget VPS, virtual private servers, shared bandwidth VPS, affordable VPS hosting, 1gbps shared VPS">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-slate-950 text-gray-300">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <!-- Left Content -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                BUDGET FRIENDLY
                            </div>
                            <div class="text-gray-400 text-sm">💰 Starting €4.99/month</div>
                        </div>

                        <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                            Shared<br>
                            <span class="bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 bg-clip-text text-transparent">
                                VPS Hosting
                            </span>
                        </h1>

                        <p class="text-xl text-gray-300 leading-relaxed mb-8">
                            Cost-effective virtual private servers with shared 1Gbps bandwidth. Perfect for small to medium projects, development environments, and budget-conscious hosting needs.
                        </p>

                        <div class="grid grid-cols-2 gap-6 mb-8">
                            <div class="bg-slate-800/30 p-4 rounded-xl border border-orange-500/20">
                                <div class="text-2xl font-bold text-white mb-2">1 Gbps</div>
                                <div class="text-orange-400 text-sm">Shared Bandwidth</div>
                            </div>
                            <div class="bg-slate-800/30 p-4 rounded-xl border border-red-500/20">
                                <div class="text-2xl font-bold text-white mb-2">€4.99</div>
                                <div class="text-red-400 text-sm">Starting Price</div>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#shared-vps-plans" class="btn-primary px-8 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="server" class="w-5 h-5 mr-2"></i>
                                View Plans
                            </a>
                            <a href="#features" class="btn-secondary px-8 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                                Learn More
                            </a>
                        </div>
                    </div>

                    <!-- Right Content - Server Visualization -->
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-3xl blur-3xl"></div>
                        <div class="relative bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-3xl p-8">
                            <div class="text-center mb-6">
                                <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="share-2" class="w-10 h-10 text-white"></i>
                                </div>
                                <h3 class="text-xl font-bold text-white mb-2">Shared Infrastructure</h3>
                                <p class="text-gray-400 text-sm">Optimized resource sharing for maximum value</p>
                            </div>

                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                                    <span class="text-gray-300 text-sm">Network Speed</span>
                                    <span class="text-orange-400 font-semibold">1 Gbps Shared</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                                    <span class="text-gray-300 text-sm">Storage Type</span>
                                    <span class="text-red-400 font-semibold">SSD</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                                    <span class="text-gray-300 text-sm">Virtualization</span>
                                    <span class="text-pink-400 font-semibold">KVM</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                                    <span class="text-gray-300 text-sm">Traffic</span>
                                    <span class="text-green-400 font-semibold">Unmetered</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Key Features Section -->
        <section id="features" class="py-20 bg-slate-900/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Why Choose Shared VPS?</h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">Get the benefits of VPS hosting at a fraction of the cost with our shared bandwidth model</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div class="feature-card p-6 rounded-xl">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="dollar-sign" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Cost Effective</h3>
                        <p class="text-gray-400">Shared bandwidth model reduces costs while maintaining excellent performance for most use cases.</p>
                    </div>

                    <div class="feature-card p-6 rounded-xl">
                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Instant Deployment</h3>
                        <p class="text-gray-400">Your shared VPS is ready in under 60 seconds after payment confirmation.</p>
                    </div>

                    <div class="feature-card p-6 rounded-xl">
                        <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center mb-4">
                            <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Full Root Access</h3>
                        <p class="text-gray-400">Complete control over your virtual server with full root access and custom configurations.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Shared VPS Configurator Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Choose Your Shared VPS Plan</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        All plans share 1Gbps bandwidth and include unmetered traffic, instant deployment, and enterprise-grade infrastructure.
                    </p>
                </div>

                <!-- Bandwidth Info Banner -->
                <div class="bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-6 mb-12">
                    <div class="flex items-center justify-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-4">
                            <i data-lucide="share-2" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="text-center">
                            <h3 class="text-xl font-bold text-white mb-2">Shared 1 Gbps Network</h3>
                            <p class="text-gray-300">All VPS instances share a high-speed 1Gbps connection, providing excellent performance at budget-friendly prices</p>
                        </div>
                    </div>
                </div>

                <!-- Shared VPS Pricing Section -->
                <section id="shared-vps-plans" class="py-20">
                    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="text-center mb-16">
                            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Shared VPS Hosting Plans</h2>
                            <p class="text-gray-400 max-w-2xl mx-auto">All plans include unmetered traffic, shared 1Gbps network, and instant deployment</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Shared VPS Plan 1 -->
                            <div class="group relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                                <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-orange-500/50 hover:bg-slate-800/50">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 class="text-xl font-bold text-white">Shared VPS Starter</h4>
                                            <p class="text-orange-400 font-medium text-sm">Budget Entry</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-white">€4.99</div>
                                            <div class="text-gray-400 text-xs">/month</div>
                                        </div>
                                    </div>

                                    <div class="space-y-3 mb-6 text-sm">
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">1 GB RAM</span>
                                            <span class="text-gray-300">1 vCPU Core</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">15 GB SSD Storage</span>
                                            <span class="text-orange-400 font-semibold">1Gbps Shared</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">Unmetered Traffic</span>
                                            <span class="text-green-400">✓ Included</span>
                                        </div>
                                    </div>

                                    <div class="flex gap-3">
                                        <button class="flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-orange-600 hover:to-red-700 transition-all duration-300">
                                            Order Now
                                        </button>
                                        <button class="px-4 py-2 border border-orange-500/50 text-orange-400 rounded-lg hover:bg-orange-500/10 transition-colors">
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Shared VPS Plan 2 -->
                            <div class="group relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                                <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 class="text-xl font-bold text-white">Shared VPS Standard</h4>
                                            <p class="text-red-400 font-medium text-sm">Most Popular</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-white">€8.99</div>
                                            <div class="text-gray-400 text-xs">/month</div>
                                        </div>
                                    </div>

                                    <div class="space-y-3 mb-6 text-sm">
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">2 GB RAM</span>
                                            <span class="text-gray-300">1 vCPU Core</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">30 GB SSD Storage</span>
                                            <span class="text-red-400 font-semibold">1Gbps Shared</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">Unmetered Traffic</span>
                                            <span class="text-green-400">✓ Included</span>
                                        </div>
                                    </div>

                                    <div class="flex gap-3">
                                        <button class="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-red-600 hover:to-pink-700 transition-all duration-300">
                                            Order Now
                                        </button>
                                        <button class="px-4 py-2 border border-red-500/50 text-red-400 rounded-lg hover:bg-red-500/10 transition-colors">
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Shared VPS Plan 3 -->
                            <div class="group relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-purple-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                                <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-pink-500/50 hover:bg-slate-800/50">
                                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <div class="bg-gradient-to-r from-pink-600 to-purple-600 text-white px-4 py-1 rounded-full text-xs font-bold">
                                            BEST VALUE
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between mb-4 mt-2">
                                        <div>
                                            <h4 class="text-xl font-bold text-white">Shared VPS Pro</h4>
                                            <p class="text-pink-400 font-medium text-sm">Dual-Core Power</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-white">€14.99</div>
                                            <div class="text-gray-400 text-xs">/month</div>
                                        </div>
                                    </div>

                                    <div class="space-y-3 mb-6 text-sm">
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">4 GB RAM</span>
                                            <span class="text-gray-300">2 vCPU Cores</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">60 GB SSD Storage</span>
                                            <span class="text-pink-400 font-semibold">1Gbps Shared</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">Unmetered Traffic</span>
                                            <span class="text-green-400">✓ Included</span>
                                        </div>
                                    </div>

                                    <div class="flex gap-3">
                                        <button class="flex-1 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300">
                                            Order Now
                                        </button>
                                        <button class="px-4 py-2 border border-pink-500/50 text-pink-400 rounded-lg hover:bg-pink-500/10 transition-colors">
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Shared VPS Plan 4 -->
                            <div class="group relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-indigo-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                                <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-purple-500/50 hover:bg-slate-800/50">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 class="text-xl font-bold text-white">Shared VPS Max</h4>
                                            <p class="text-purple-400 font-medium text-sm">High Performance</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-white">€24.99</div>
                                            <div class="text-gray-400 text-xs">/month</div>
                                        </div>
                                    </div>

                                    <div class="space-y-3 mb-6 text-sm">
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">8 GB RAM</span>
                                            <span class="text-gray-300">4 vCPU Cores</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">120 GB SSD Storage</span>
                                            <span class="text-purple-400 font-semibold">1Gbps Shared</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">Unmetered Traffic</span>
                                            <span class="text-green-400">✓ Included</span>
                                        </div>
                                    </div>

                                    <div class="flex gap-3">
                                        <button class="flex-1 bg-gradient-to-r from-purple-500 to-indigo-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-600 hover:to-indigo-700 transition-all duration-300">
                                            Order Now
                                        </button>
                                        <button class="px-4 py-2 border border-purple-500/50 text-purple-400 rounded-lg hover:bg-purple-500/10 transition-colors">
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Shared VPS Plan 5 -->
                            <div class="group relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                                <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-indigo-500/50 hover:bg-slate-800/50">
                                    <div class="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 class="text-xl font-bold text-white">Shared VPS Ultra</h4>
                                            <p class="text-indigo-400 font-medium text-sm">Maximum Resources</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-white">€39.99</div>
                                            <div class="text-gray-400 text-xs">/month</div>
                                        </div>
                                    </div>

                                    <div class="space-y-3 mb-6 text-sm">
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">16 GB RAM</span>
                                            <span class="text-gray-300">6 vCPU Cores</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">240 GB SSD Storage</span>
                                            <span class="text-indigo-400 font-semibold">1Gbps Shared</span>
                                        </div>
                                        <div class="flex items-center justify-between py-1">
                                            <span class="text-gray-300">Unmetered Traffic</span>
                                            <span class="text-green-400">✓ Included</span>
                                        </div>
                                    </div>

                                    <div class="flex gap-3">
                                        <button class="flex-1 bg-gradient-to-r from-indigo-500 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-indigo-600 hover:to-blue-700 transition-all duration-300">
                                            Order Now
                                        </button>
                                        <button class="px-4 py-2 border border-indigo-500/50 text-indigo-400 rounded-lg hover:bg-indigo-500/10 transition-colors">
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </section>

        <!-- Technical Specifications Section -->
        <section class="py-20 bg-slate-900/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Technical Specifications</h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">Enterprise-grade infrastructure with shared bandwidth for optimal cost-efficiency</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <div>
                        <div class="bg-slate-800/30 rounded-xl p-8 border border-slate-700/50">
                            <h3 class="text-xl font-bold text-white mb-6">Hardware & Infrastructure</h3>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Enterprise SSDs</div>
                                        <div class="text-gray-400 text-sm">High-performance NVMe and SSD storage arrays</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Premium CPUs</div>
                                        <div class="text-gray-400 text-sm">Latest Intel Xeon and AMD EPYC processors</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Shared 1Gbps Network</div>
                                        <div class="text-gray-400 text-sm">High-speed shared bandwidth for cost-effective hosting</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">KVM Virtualization</div>
                                        <div class="text-gray-400 text-sm">Full virtualization with dedicated resources</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="bg-slate-800/30 rounded-xl p-8 border border-slate-700/50">
                            <h3 class="text-xl font-bold text-white mb-6">Features & Support</h3>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-indigo-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Full Root Access</div>
                                        <div class="text-gray-400 text-sm">Complete control over your virtual environment</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Multiple OS Support</div>
                                        <div class="text-gray-400 text-sm">Ubuntu, CentOS, Debian, and custom installations</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-cyan-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">24/7 Monitoring</div>
                                        <div class="text-gray-400 text-sm">Proactive monitoring and instant issue resolution</div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-teal-400 rounded-full mr-3"></div>
                                    <div>
                                        <div class="text-white font-medium">Backup Solutions</div>
                                        <div class="text-gray-400 text-sm">Optional automated backup services available</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Use Cases Section -->
        <section class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Perfect for Your Projects</h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">Shared VPS hosting is ideal for various applications and use cases</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-orange-500/50 hover:shadow-xl hover:shadow-orange-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="code" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Development & Testing</h3>
                            <p class="text-gray-400 mb-4">Perfect for development environments, staging servers, and testing applications without breaking the budget.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Development environments</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>CI/CD pipelines</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Testing platforms</li>
                            </ul>
                        </div>
                    </div>

                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-red-500/50 hover:shadow-xl hover:shadow-red-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Small Websites</h3>
                            <p class="text-gray-400 mb-4">Host personal websites, blogs, and small business sites with reliable performance and shared resources.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Personal blogs</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Portfolio sites</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Small business websites</li>
                            </ul>
                        </div>
                    </div>

                    <div class="group">
                        <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-xl border border-slate-700/50 h-full transition-all duration-300 hover:border-pink-500/50 hover:shadow-xl hover:shadow-pink-500/10">
                            <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                                <i data-lucide="database" class="w-8 h-8 text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-4">Learning & Education</h3>
                            <p class="text-gray-400 mb-4">Ideal for students, educators, and anyone learning server administration or web development.</p>
                            <ul class="text-sm text-gray-300 space-y-2">
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Learning environments</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Educational projects</li>
                                <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 text-green-400 mr-2"></i>Skill development</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Performance Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">99.9%</div>
                        <div class="text-gray-400 text-sm">Uptime SLA</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">&lt;60s</div>
                        <div class="text-gray-400 text-sm">Deployment Time</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">24/7</div>
                        <div class="text-gray-400 text-sm">Support Available</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">1Gbps</div>
                        <div class="text-gray-400 text-sm">Shared Network</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20 bg-slate-900/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Shared VPS FAQ</h2>
                    <p class="text-gray-400 mt-2">Common questions about our shared VPS hosting services.</p>
                </div>
                <div class="space-y-4">
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What does "shared 1Gbps bandwidth" mean?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Shared 1Gbps bandwidth means all VPS instances on the same physical server share a high-speed 1Gbps network connection. This provides excellent performance for most applications while keeping costs low. Your VPS will have access to the full 1Gbps when available, but during peak usage, the bandwidth is fairly distributed among all instances.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            How is this different from dedicated VPS bandwidth?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Dedicated VPS bandwidth guarantees a specific amount of bandwidth exclusively for your VPS (like 2-4Gbps), while shared bandwidth pools the total available bandwidth (1Gbps) among all VPS instances. Shared bandwidth is more cost-effective and suitable for applications that don't require guaranteed high bandwidth at all times.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            Is shared VPS suitable for production websites?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Yes, shared VPS is perfect for small to medium production websites, blogs, and applications that don't require extremely high bandwidth. The shared model provides excellent value while maintaining good performance for most use cases. For high-traffic sites requiring guaranteed bandwidth, consider our dedicated VPS plans.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What operating systems are supported?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                           We support various Linux distributions including Ubuntu, CentOS, Debian, and others. Custom OS installations can also be arranged upon request. All VPS instances come with full root access for complete control.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            Can I upgrade my shared VPS plan later?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            Absolutely! You can upgrade your shared VPS plan at any time to get more resources (RAM, CPU, storage). You can also migrate to our dedicated VPS plans if you need guaranteed bandwidth. Upgrades are processed quickly with minimal downtime.
                        </div>
                    </details>
                    <details class="faq-item pt-4">
                        <summary class="flex justify-between items-center text-lg font-medium text-white pb-4">
                            What kind of support do you provide?
                            <i data-lucide="chevron-down" class="w-5 h-5 transition-transform transform"></i>
                        </summary>
                        <div class="pb-4 text-gray-400">
                            We provide 24/7 technical support for all shared VPS customers. Our support team can help with server setup, basic configuration, and troubleshooting. We also offer managed services for customers who prefer hands-off server management.
                        </div>
                    </details>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        lucide.createIcons();

        // FAQ accordion behavior
        const detailsElements = document.querySelectorAll('details');
        detailsElements.forEach(details => {
            details.addEventListener('toggle', event => {
                const icon = details.querySelector('summary i');
                if (details.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // Animated lines background
        const canvas = document.getElementById('lines-canvas');
        const ctx = canvas.getContext('2d');

        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        const lines = [];
        const numLines = 8;

        // Create lines with shared VPS-themed colors (oranges, reds, pinks)
        for (let i = 0; i < numLines; i++) {
            lines.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                length: Math.random() * 200 + 100,
                angle: Math.random() * Math.PI * 2,
                speed: Math.random() * 0.5 + 0.2,
                opacity: Math.random() * 0.3 + 0.1,
                color: i % 5 === 0 ? '#f97316' : i % 5 === 1 ? '#ef4444' : i % 5 === 2 ? '#ec4899' : i % 5 === 3 ? '#a855f7' : '#6366f1'
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            lines.forEach(line => {
                // Update position
                line.x += Math.cos(line.angle) * line.speed;
                line.y += Math.sin(line.angle) * line.speed;

                // Wrap around edges
                if (line.x < -line.length) line.x = canvas.width + line.length;
                if (line.x > canvas.width + line.length) line.x = -line.length;
                if (line.y < -line.length) line.y = canvas.height + line.length;
                if (line.y > canvas.height + line.length) line.y = -line.length;

                // Draw line with gradient
                const gradient = ctx.createLinearGradient(
                    line.x, line.y,
                    line.x + Math.cos(line.angle) * line.length,
                    line.y + Math.sin(line.angle) * line.length
                );
                gradient.addColorStop(0, line.color + '00');
                gradient.addColorStop(0.5, line.color + Math.floor(line.opacity * 255).toString(16).padStart(2, '0'));
                gradient.addColorStop(1, line.color + '00');

                ctx.strokeStyle = gradient;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(line.x, line.y);
                ctx.lineTo(
                    line.x + Math.cos(line.angle) * line.length,
                    line.y + Math.sin(line.angle) * line.length
                );
                ctx.stroke();
            });

            requestAnimationFrame(animate);
        }

        animate();

        // Load header and footer
        fetch('header-loader.js')
            .then(response => response.text())
            .then(script => eval(script))
            .catch(error => console.error('Error loading header:', error));

        fetch('footer-loader.js')
            .then(response => response.text())
            .then(script => eval(script))
            .catch(error => console.error('Error loading footer:', error));
    </script>
</body>
</html>
