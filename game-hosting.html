<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Server Hosting - X-ZoneServers | High-Performance Gaming Servers</title>
    <meta name="description" content="X-ZoneServers premium game server hosting with ultra-low latency, DDoS protection, and instant setup. Perfect for Minecraft, CS2, Rust, and more.">
    <meta name="keywords" content="game server hosting, minecraft hosting, CS2 servers, rust hosting, gaming servers, low latency hosting">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>

</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <!-- Left Content -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                GAMING OPTIMIZED
                            </div>
                            <div class="text-gray-400 text-sm">🎮 Ultra-Low Latency</div>
                        </div>

                        <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                            Elite<br>
                            <span class="bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 bg-clip-text text-transparent">
                                Game Hosting
                            </span>
                        </h1>

                        <p class="text-xl text-gray-300 mb-8">
                            Dominate the competition with our premium game server hosting. Ultra-low latency, enterprise DDoS protection, and instant deployment for all major games.
                        </p>

                        <div class="flex flex-wrap gap-6 mb-12">
                            <div class="flex items-center text-green-400">
                                <i data-lucide="zap" class="w-5 h-5 mr-2"></i>
                                <span class="font-semibold">Instant Setup</span>
                            </div>
                            <div class="flex items-center text-blue-400">
                                <i data-lucide="shield" class="w-5 h-5 mr-2"></i>
                                <span class="font-semibold">DDoS Protection</span>
                            </div>
                            <div class="flex items-center text-purple-400">
                                <i data-lucide="activity" class="w-5 h-5 mr-2"></i>
                                <span class="font-semibold">99.9% Uptime</span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#game-plans" class="group bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white px-8 py-4 rounded-2xl font-bold text-lg inline-flex items-center justify-center shadow-2xl hover:shadow-orange-500/25 transition-all duration-300 transform hover:scale-105">
                                <i data-lucide="gamepad-2" class="w-6 h-6 mr-3 group-hover:animate-bounce"></i>
                                Choose Your Game
                                <i data-lucide="arrow-right" class="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform"></i>
                            </a>
                            <a href="#features" class="group bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 text-white px-8 py-4 rounded-2xl font-bold text-lg inline-flex items-center justify-center hover:bg-slate-700/50 transition-all duration-300 transform hover:scale-105">
                                <i data-lucide="play-circle" class="w-6 h-6 mr-3"></i>
                                View Demo
                            </a>
                        </div>
                    </div>

                    <!-- Right Content - Gaming Stats -->
                    <div class="relative">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="bg-gradient-to-br from-orange-500/10 to-red-600/10 backdrop-blur-sm border border-orange-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="zap" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-white mb-1">&lt;5ms</div>
                                <div class="text-orange-400 text-sm">Latency</div>
                            </div>

                            <div class="bg-gradient-to-br from-red-500/10 to-pink-600/10 backdrop-blur-sm border border-red-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-white mb-1">1Tbps</div>
                                <div class="text-red-400 text-sm">DDoS Protection</div>
                            </div>

                            <div class="bg-gradient-to-br from-pink-500/10 to-purple-600/10 backdrop-blur-sm border border-pink-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="users" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-white mb-1">1000+</div>
                                <div class="text-pink-400 text-sm">Players</div>
                            </div>

                            <div class="bg-gradient-to-br from-purple-500/10 to-blue-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="globe" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-2xl font-bold text-white mb-1">13</div>
                                <div class="text-purple-400 text-sm">Locations</div>
                            </div>
                        </div>

                        <!-- Floating elements -->
                        <div class="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-orange-500/20 to-red-600/20 rounded-full blur-2xl animate-pulse"></div>
                        <div class="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-pink-500/20 to-purple-600/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Games Section -->
        <section id="games" class="py-24 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Popular <span class="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">Game Servers</span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Choose from our extensive library of pre-configured game servers. One-click installation and instant deployment.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Minecraft -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-green-500/50 transition-all duration-300 transform hover:scale-105">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="box" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4 text-center">Minecraft</h3>
                        <p class="text-gray-400 text-center mb-6">Build, explore, and survive with friends. Supports all versions and modpacks.</p>
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Starting from:</span>
                                <span class="text-green-400 font-bold">€4.99/month</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Max Players:</span>
                                <span class="text-white">Unlimited</span>
                            </div>
                        </div>
                        <a href="#minecraft-plans" class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300">
                            Configure Server
                        </a>
                    </div>

                    <!-- Counter-Strike 2 -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-orange-500/50 transition-all duration-300 transform hover:scale-105">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="crosshair" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4 text-center">Counter-Strike 2</h3>
                        <p class="text-gray-400 text-center mb-6">Competitive FPS gaming with ultra-low latency and anti-cheat protection.</p>
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Starting from:</span>
                                <span class="text-orange-400 font-bold">€9.99/month</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Max Players:</span>
                                <span class="text-white">64</span>
                            </div>
                        </div>
                        <a href="#cs2-plans" class="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300">
                            Configure Server
                        </a>
                    </div>

                    <!-- Rust -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-red-500/50 transition-all duration-300 transform hover:scale-105">
                        <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="flame" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4 text-center">Rust</h3>
                        <p class="text-gray-400 text-center mb-6">Survival multiplayer with base building, PvP combat, and resource gathering.</p>
                        <div class="space-y-3 mb-6">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Starting from:</span>
                                <span class="text-red-400 font-bold">€14.99/month</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">Max Players:</span>
                                <span class="text-white">200</span>
                            </div>
                        </div>
                        <a href="#rust-plans" class="w-full bg-gradient-to-r from-red-500 to-pink-600 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300">
                            Configure Server
                        </a>
                    </div>
                </div>

                <!-- More Games Button -->
                <div class="text-center mt-12">
                    <a href="#all-games" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-slate-800 to-slate-700 text-white font-semibold rounded-2xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 transform hover:scale-105">
                        <i data-lucide="grid-3x3" class="w-5 h-5 mr-3"></i>
                        View All Games
                        <i data-lucide="arrow-right" class="w-5 h-5 ml-3"></i>
                    </a>
                </div>
            </div>
        </section>

        <!-- Game Hosting Plans -->
        <section id="game-plans" class="py-24 bg-slate-950">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Game Hosting <span class="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">Plans</span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Choose the perfect plan for your gaming community. All plans include DDoS protection and 24/7 support.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    <!-- Starter Plan -->
                    <div class="bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-green-500/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-white mb-2">Starter</h3>
                            <p class="text-gray-400 mb-6">Perfect for small communities</p>
                            <div class="text-4xl font-bold text-white mb-2">€9.99</div>
                            <div class="text-gray-400">/month</div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i>
                                Up to 20 Players
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i>
                                4GB RAM
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i>
                                50GB SSD Storage
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i>
                                DDoS Protection
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-green-400 mr-3"></i>
                                24/7 Support
                            </li>
                        </ul>

                        <a href="#order" class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300">
                            Get Started
                        </a>
                    </div>

                    <!-- Pro Plan (Popular) -->
                    <div class="relative bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border-2 border-orange-500/50 rounded-2xl p-8 transform scale-105">
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-2 rounded-full text-sm font-bold">
                                MOST POPULAR
                            </div>
                        </div>

                        <div class="text-center mb-8 mt-4">
                            <h3 class="text-2xl font-bold text-white mb-2">Pro</h3>
                            <p class="text-gray-400 mb-6">Best for growing communities</p>
                            <div class="text-4xl font-bold text-white mb-2">€19.99</div>
                            <div class="text-gray-400">/month</div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                Up to 50 Players
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                8GB RAM
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                100GB SSD Storage
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                Priority DDoS Protection
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                Priority Support
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-orange-400 mr-3"></i>
                                Free Backups
                            </li>
                        </ul>

                        <a href="#order" class="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300">
                            Get Started
                        </a>
                    </div>

                    <!-- Enterprise Plan -->
                    <div class="bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-white mb-2">Enterprise</h3>
                            <p class="text-gray-400 mb-6">For large gaming communities</p>
                            <div class="text-4xl font-bold text-white mb-2">€39.99</div>
                            <div class="text-gray-400">/month</div>
                        </div>

                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                Up to 100 Players
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                16GB RAM
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                200GB SSD Storage
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                Enterprise DDoS Protection
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                Dedicated Support
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                Daily Backups
                            </li>
                            <li class="flex items-center text-gray-300">
                                <i data-lucide="check" class="w-5 h-5 text-purple-400 mr-3"></i>
                                Custom Configurations
                            </li>
                        </ul>

                        <a href="#order" class="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-semibold text-center block hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300">
                            Get Started
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gaming Features -->
        <section id="features" class="py-24 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                        Gaming <span class="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">Features</span>
                    </h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Everything you need for the ultimate gaming experience. Built by gamers, for gamers.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Ultra-Low Latency -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-orange-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Ultra-Low Latency</h3>
                        <p class="text-gray-400">Experience sub-5ms latency with our optimized network infrastructure and strategic server locations worldwide.</p>
                    </div>

                    <!-- DDoS Protection -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="shield" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Advanced DDoS Protection</h3>
                        <p class="text-gray-400">Stay online with our enterprise-grade DDoS protection, filtering up to 1Tbps of malicious traffic.</p>
                    </div>

                    <!-- Instant Setup -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-green-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="play" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Instant Setup</h3>
                        <p class="text-gray-400">Get your game server running in under 60 seconds with our automated deployment system.</p>
                    </div>

                    <!-- 24/7 Support -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="headphones" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">24/7 Expert Support</h3>
                        <p class="text-gray-400">Our gaming specialists are available around the clock to help with any server issues or configurations.</p>
                    </div>

                    <!-- Auto Backups -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-yellow-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="hard-drive" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Automatic Backups</h3>
                        <p class="text-gray-400">Never lose your progress with automated daily backups and one-click restore functionality.</p>
                    </div>

                    <!-- Global Network -->
                    <div class="group bg-gradient-to-br from-slate-900/50 to-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 hover:border-cyan-500/50 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Global Network</h3>
                        <p class="text-gray-400">Choose from 13 strategic locations worldwide to minimize latency for your players.</p>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Animated lines background
        const canvas = document.getElementById('lines-canvas');
        const ctx = canvas.getContext('2d');

        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        const lines = [];
        const numLines = 50;

        for (let i = 0; i < numLines; i++) {
            lines.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                dx: (Math.random() - 0.5) * 2,
                dy: (Math.random() - 0.5) * 2,
                length: Math.random() * 100 + 50,
                angle: Math.random() * Math.PI * 2,
                speed: Math.random() * 0.02 + 0.01,
                opacity: Math.random() * 0.5 + 0.1
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            lines.forEach(line => {
                line.x += line.dx;
                line.y += line.dy;
                line.angle += line.speed;

                if (line.x < 0 || line.x > canvas.width) line.dx *= -1;
                if (line.y < 0 || line.y > canvas.height) line.dy *= -1;

                const gradient = ctx.createLinearGradient(
                    line.x, line.y,
                    line.x + Math.cos(line.angle) * line.length,
                    line.y + Math.sin(line.angle) * line.length
                );
                gradient.addColorStop(0, `rgba(249, 115, 22, ${line.opacity})`);
                gradient.addColorStop(0.5, `rgba(239, 68, 68, ${line.opacity})`);
                gradient.addColorStop(1, `rgba(236, 72, 153, 0)`);

                ctx.strokeStyle = gradient;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(line.x, line.y);
                ctx.lineTo(
                    line.x + Math.cos(line.angle) * line.length,
                    line.y + Math.sin(line.angle) * line.length
                );
                ctx.stroke();
            });

            requestAnimationFrame(animate);
        }

        animate();
    </script>
</body>
</html>
